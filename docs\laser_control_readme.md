# 激光控制功能说明

## 功能概述
当两个激光的x轴坐标（latest_green_laser_coord.x与latest_red_laser_coord.x）一致时，将GPIO引脚qyjg_Pin拉高，使激光亮起，5秒后自动熄灭。

## 技术实现

### 文件结构
- `bsp/laser_control_bsp.h` - 激光控制模块头文件
- `bsp/laser_control_bsp.c` - 激光控制模块实现文件

### 状态机设计
1. **LASER_IDLE** - 空闲状态，等待坐标一致
2. **LASER_CHECKING** - 检查状态，进行防抖处理
3. **LASER_TRIGGERED** - 触发状态，准备点亮激光
4. **LASER_ON** - 激光亮起状态，持续5秒
5. **LASER_OFF** - 激光熄灭状态，然后回到空闲

### 关键参数
- **执行周期**: 10ms
- **防抖时间**: 50ms（5个执行周期）
- **激光亮起时间**: 5000ms（5秒）
- **控制引脚**: qyjg_Pin (PB1)

### 防抖机制
坐标一致状态需要持续50ms（5个10ms周期）才会触发激光，避免坐标抖动造成的误触发。

### 安全特性
- 初始化时确保激光处于关闭状态
- 异常状态自动重置到空闲状态
- 状态机确保激光只能亮起5秒

## 集成说明

### 调度系统集成
激光控制任务已添加到schedule.c的任务数组中：
```c
{laser_control_proc, 10, 0}  // 10ms执行周期
```

### 初始化集成
在main.c中添加了初始化调用：
```c
laser_control_init();
```

### 系统头文件集成
在bsp_system.h中添加了模块包含：
```c
#include "laser_control_bsp.h"
```

## 使用注意事项
1. 确保激光坐标数据有效（isValid标志为1）
2. 激光控制不会影响现有的PID控制和步进电机功能
3. GPIO引脚qyjg_Pin已在系统初始化时配置为输出模式
4. 激光亮起期间，即使坐标不再一致，也会完成5秒的亮起周期

## 调试信息
可以通过监控以下变量来调试激光控制状态：
- `laser_state` - 当前状态机状态
- `debounce_counter` - 防抖计数器
- `latest_green_laser_coord.x` - 绿色激光X坐标
- `latest_red_laser_coord.x` - 红色激光X坐标
