#ifndef __LASER_CONTROL_BSP_H__
#define __LASER_CONTROL_BSP_H__

#include "bsp_system.h"

// 激光控制状态枚举
typedef enum {
    LASER_IDLE = 0,        // 空闲状态
    LASER_CHECKING,        // 检查坐标一致性
    LASER_TRIGGERED,       // 触发状态
    LASER_ON,              // 激光亮起
    LASER_OFF              // 激光熄灭
} LaserControlState_t;

// 激光控制配置参数
#define LASER_DEBOUNCE_TIME_MS    50    // 防抖时间50ms
#define LASER_ON_TIME_MS          5000  // 激光亮起时间5秒
#define LASER_DEBOUNCE_COUNT      5     // 防抖计数（50ms/10ms周期）

// 函数声明
void laser_control_init(void);
void laser_control_proc(void);

#endif
