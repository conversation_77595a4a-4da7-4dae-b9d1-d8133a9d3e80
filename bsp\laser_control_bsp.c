#include "laser_control_bsp.h"

// 激光控制状态变量
static LaserControlState_t laser_state = LASER_IDLE;
static uint32_t trigger_timestamp = 0;
static uint8_t debounce_counter = 0;
static uint32_t laser_on_timestamp = 0;

/**
 * @brief 激光控制模块初始化
 */
void laser_control_init(void)
{
    laser_state = LASER_IDLE;
    trigger_timestamp = 0;
    debounce_counter = 0;
    laser_on_timestamp = 0;
    
    // 确保激光初始状态为关闭
    HAL_GPIO_WritePin(qyjg_GPIO_Port, qyjg_Pin, GPIO_PIN_RESET);
}

/**
 * @brief 激光控制主处理函数
 * 每10ms调用一次，处理激光控制逻辑
 */
void laser_control_proc(void)
{
    uint32_t current_time = HAL_GetTick();
    
    // 检查激光坐标是否一致
    uint8_t coords_match = (latest_green_laser_coord.x == latest_red_laser_coord.x) &&
                          (latest_green_laser_coord.isValid && latest_red_laser_coord.isValid);
    
    switch (laser_state)
    {
        case LASER_IDLE:
            if (coords_match)
            {
                laser_state = LASER_CHECKING;
                debounce_counter = 1;
                trigger_timestamp = current_time;
            }
            break;
            
        case LASER_CHECKING:
            if (coords_match)
            {
                debounce_counter++;
                if (debounce_counter >= LASER_DEBOUNCE_COUNT)
                {
                    // 防抖完成，触发激光
                    laser_state = LASER_TRIGGERED;
                }
            }
            else
            {
                // 坐标不一致，回到空闲状态
                laser_state = LASER_IDLE;
                debounce_counter = 0;
            }
            break;
            
        case LASER_TRIGGERED:
            // 激光亮起
            HAL_GPIO_WritePin(qyjg_GPIO_Port, qyjg_Pin, GPIO_PIN_SET);
            laser_on_timestamp = current_time;
            laser_state = LASER_ON;
            break;
            
        case LASER_ON:
            // 检查是否到达5秒时间
            if (current_time - laser_on_timestamp >= LASER_ON_TIME_MS)
            {
                laser_state = LASER_OFF;
            }
            break;
            
        case LASER_OFF:
            // 激光熄灭
            HAL_GPIO_WritePin(qyjg_GPIO_Port, qyjg_Pin, GPIO_PIN_RESET);
            laser_state = LASER_IDLE;
            debounce_counter = 0;
            break;
            
        default:
            // 异常状态，重置到空闲状态
            laser_state = LASER_IDLE;
            debounce_counter = 0;
            HAL_GPIO_WritePin(qyjg_GPIO_Port, qyjg_Pin, GPIO_PIN_RESET);
            break;
    }
}
